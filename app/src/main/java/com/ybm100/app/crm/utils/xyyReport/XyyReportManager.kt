package com.ybm100.app.crm.utils.xyyReport

import android.content.Context
import android.webkit.WebView
import com.quick.qt.analytics.QtTrackAgent
import com.quick.qt.commonsdk.QtConfigure
import com.quick.qt.spm.SpmAgent
import com.ybmmarket20.xyyreport.session.WatchAppLifecycle

/**
 * 埋点管理类
 */
class XyyReportManager {

    companion object {

        /**
         * 未授权合规协议
         */
        @JvmStatic
        fun preInit(context: Context) {
            QtConfigure.setCustomDomain("https://qt.ybm100.com", null)
            setLogEnabled(BuildConfig.DEBUG)
            QtConfigure.preInit(context, BuildConfig.qt_app_key, "")
            closeAutoActivityPVAllTrack()
            WatchAppLifecycle.get().register()
        }

        /**
         * 已授权合规协议
         */
        @JvmStatic
        fun init(context: Context) {
            QtConfigure.setCustomDomain("https://qt.ybm100.com", null)
            setLogEnabled(BuildConfig.DEBUG)
            QtConfigure.init(context, BuildConfig.qt_app_key, "", QtConfigure.DEVICE_TYPE_PHONE, null)
            closeAutoActivityPVAllTrack()
            WatchAppLifecycle.get().register()
//            if (BuildConfig.DEBUG) {
//                val sp = context.getSharedPreferences("user_info", Context.MODE_PRIVATE)
//                val isCheck = sp.getBoolean("qtAllTrackSwitch", false)
//                setAutoFragmentPVAllTrack(isCheck)
//                openWidgetClickAllTrack(isCheck)
//                if (!isCheck) {
//                    closeAutoActivityPVAllTrack()
//                }
//            } else {
//                setAutoFragmentPVAllTrack(true)
//                openWidgetClickAllTrack(true)
//            }
        }

        /**
         * 设置日志开关
         */
        fun setLogEnabled(enable: Boolean) {
            QtConfigure.setLogEnabled(enable)
        }

        /**
         * 埋点登入
         */
        @JvmStatic
        fun signIn(userId: String) {
            QtTrackAgent.onProfileSignIn(userId)
        }

        /**
         * 埋点登出
         */
        @JvmStatic
        fun signOff() {
            QtTrackAgent.onProfileSignOff()
        }

        /**
         * 设置用户属性
         */
        @JvmStatic
        fun setUserProperties(context: Context, userProperties: Map<String, Any>) {
            QtTrackAgent.onEventObject(context, "$\$_user_profile", userProperties)
        }

        /**
         * 设置全局属性
         */
        @JvmStatic
        fun setGlobalProperties(context: Context, globalProperties: Map<String, Any>) {
            QtTrackAgent.registerGlobalProperties(context, globalProperties)
        }

        /**
         * 获取单个全局属性
         */
        @JvmStatic
        fun getSingleGlobalProperty(context: Context, propertyKey: String): Any? {
            return QtTrackAgent.getGlobalProperty(context, propertyKey)
        }

        /**
         * 获取单个全局属性
         */
        @JvmStatic
        fun getAllGlobalProperties(context: Context): String? {
            return QtTrackAgent.getGlobalProperties(context)
        }

        /**
         * 设置页面属性
         */
        @JvmStatic
        fun setPageProperties(context: Context, pageName: String, pageProperties: Map<String, Any>) {
            QtTrackAgent.setPageProperty(context, pageName, pageProperties)
        }

        /**
         * 透传数据
         */
        @JvmStatic
        fun sendNextPageProperties(context: Context, pageName: String, properties: Map<String, Any>) {
            SpmAgent.updateCurSpm(context, pageName)
            SpmAgent.updateNextPageProperties(properties)
        }

        /**
         * 接受上一页透传数据
         */
        @JvmStatic
        fun receivePrePageProperties(pageName: String): Map<String, Any> {
            return SpmAgent.getPageProperties(pageName)
        }

        /**
         * 事件采样率控制
         */
        @JvmStatic
        fun setAutoEventEnabled(enable: Boolean) {
            QtTrackAgent.setAutoEventEnabled(enable)
        }

        /**
         * 设置FragmentPV自动全埋点
         */
        @JvmStatic
        fun setAutoFragmentPVAllTrack(enable: Boolean) {
            QtTrackAgent.enableFragmentPageCollection(enable)
        }

        /**
         * 关闭ActivityPV自动全埋点
         */
        @JvmStatic
        fun closeAutoActivityPVAllTrack() {
            QtTrackAgent.disableActivityPageCollection()
        }

        /**
         * 关闭指定Activity全埋点
         */
        @JvmStatic
        fun closeAutoSingleActivityPVTrack(context: Context) {
            QtTrackAgent.skipMe(context, null)
        }

        /**
         * 开启控件点击全埋点（部分系统提供控件）
         */
        @JvmStatic
        fun openWidgetClickAllTrack(enable: Boolean) {
            QtTrackAgent.setAutoEventEnabled(enable)
        }

        /**
         * 关联webview
         */
        @JvmStatic
        fun attachWebView(wv: WebView) {
            SpmAgent.attach(wv)
        }

        /**
         * 关联webview
         */
        @JvmStatic
        fun attachX5WebView(x5WebView: Any) {
            SpmAgent.attachX5(x5WebView)
        }

        /**
         * 解除关联webview
         */
        @JvmStatic
        fun detach() {
            SpmAgent.detach()
        }

        /**
         * 设置登录信息
         */
        @JvmStatic
        fun setSignInfo(context: Context, merchantId: String, accountId: String) {
            signIn(merchantId)
            val globalInfo: MutableMap<String, Any> = HashMap()
            globalInfo["account_id"] = accountId
            setGlobalProperties(context, globalInfo)
        }

    }
}