package com.ybm100.app.crm.utils.xyyReport

import android.content.Context
import com.xyy.xyyreport.IReport
import com.ybmmarket20.xyyreport.spm.IPageParams
import com.ybmmarket20.xyyreport.spm.ISpm
import com.ybmmarket20.xyyreport.spm.SpmBean
import com.ybmmarket20.xyyreport.spm.SpmUtil
import com.ybmmarket20.xyyreport.spm.XyyReportActivity

class ReportUtil {
    companion object {
        @JvmStatic
        fun track(context: Context, iReport: IReport, spmStr: String?, scmStr: String?) {
            track(context, iReport, SpmUtil.getSpmBeanFromStr(spmStr), SpmUtil.getScmBeanFromStr(scmStr))
        }

        @JvmStatic
        fun track(context: Context, iReport: IReport, spm: ISpm?, scm: ISpm?) {
            if (spm == null) return
            val isContainsSpmNull = SpmUtil.containsNull(spm)
            val isContainsScmNull = SpmUtil.containsNull(scm)
            if (isContainsSpmNull || isContainsScmNull) return
            val paramsMap = mutableMapOf<String, Any?>()
            paramsMap["spm_cnt"] = spm.concat()
            scm?.let { paramsMap["scm_cnt"] = scm.concat() }
            getPageParams(context)?.let { paramsMap.putAll(it.getSpmExtra()) }
            paramsMap.putAll(iReport.getReportParamsMap())
            try {
                val iterator = paramsMap.iterator()
                while (iterator.hasNext()) {
                    val next = iterator.next()
                    if (next.value == null) {
                        iterator.remove()
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
            EventReportManager.trackEvent(context, iReport.getReportName(), paramsMap)
        }

        @JvmStatic
        fun pvTrack(context: Context, iReport: IReport, spm: ISpm?) {
            if (spm == null) return
            SpmUtil.setNewSpmE(spm)
            if (context is XyyReportActivity && spm is SpmBean) {
                context.setSpmCtn(spm)
            }
            track(context, iReport, spm, null)
        }

        @JvmStatic
        fun pvTrack(context: Context, iReport: IReport, spmStr: String?) {
            pvTrack(context, iReport, SpmUtil.getSpmBeanFromStr(spmStr))
        }

        @JvmStatic
        fun pvTrackWithPageCode(context: Context, iReport: IReport, pageCode: String?) {
            if (pageCode == null) return
            val spmCtn = SpmUtil.getSpmPv(pageCode)
            track(context, iReport, spmCtn, null)
        }

//        @JvmStatic
//        fun pvTrack(context: Context, iReport: IReport) {
//            if (pageCode == null) return
//            val spmCtn = SpmUtil.getSpmPv(pageCode)
//            track(context, iReport, spmCtn, null)
//            getPageParams(context)?.setSpmCtn(spmCtn)
//        }

        /**
         * 组件曝光
         */
        @JvmStatic
        fun componentExposureTrack(context: Context, iReport: IReport, spm: ISpm?) {
            SpmUtil.setSpmE(context, spm)
            track(context, iReport, spm, null)
        }

        /**
         * 商品曝光
         */
        @JvmStatic
        fun goodsExposureTrack(context: Context, iReport: IReport, spm: ISpm?, scm: ISpm?) {
            SpmUtil.setSpmE(context, spm)
            track(context, iReport, spm, scm)
        }

        @JvmStatic
        fun componentExposureTrack(context: Context, iReport: IReport, spmStr: String?) {
            track(context, iReport, SpmUtil.getSpmBeanFromStr(spmStr), null)
        }

        @JvmStatic
        fun componentExposureTrackWithName(context: Context, iReport: IReport, pageCode: String?, componentName: String?) {
            track(context, iReport, SpmUtil.getSpmComponentExposure(context, pageCode, componentName), null)
        }

        @JvmStatic
        fun listCreate(context: Context, iReport: IReport, spm: ISpm?, scm: ISpm?) {
            SpmUtil.setSpmE(context, spm)
            track(context, iReport, spm, scm)
        }

        /**
         * 点击
         */
        @JvmStatic
        fun clickTrack(context: Context, iReport: IReport, spm: ISpm?, scm: ISpm?) {
            SpmUtil.setSpmE(context, spm)
            SpmUtil.setScmE(scm)
            track(context, iReport, spm, scm)
        }

        /**
         * 点击
         */
        @JvmStatic
        fun clickTrack(context: Context, iReport: IReport, spmStr: String?, scmStr: String?) {
            val spm = SpmUtil.getSpmBeanFromStr(spmStr)
            val scm = SpmUtil.getScmBeanFromStr(scmStr)
            SpmUtil.setSpmE(context, spm)
            SpmUtil.setScmE(scm)
            track(context, iReport, spm, scm)
        }

        /**
         * 切换tab时调用，刷新spm缓存
         */
        @JvmStatic
        fun switchTab(context: Context, iReport: IReport, spm: ISpm?) {
            if (spm is SpmBean) {
                pvTrack(context, iReport, spm)
            }
        }

        private fun getPageParams(context: Context): IPageParams? {
            if (context is XyyReportActivity) {
                return context
            }
            return null
        }
    }
}