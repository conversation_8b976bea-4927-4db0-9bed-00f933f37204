package com.ybm100.app.crm.utils.xyyReport

import android.content.Context
import com.quick.qt.analytics.QtTrackAgent

/**
 * 事件埋点
 */
class EventReportManager {


    companion object {

        /**
         * 页面开始
         */
        @JvmStatic
        fun onPageStart(viewName: String) {
            QtTrackAgent.onPageStart(viewName)
        }

        /**
         * 页面结束
         */
        @JvmStatic
        fun onPageEnd(viewName: String) {
            QtTrackAgent.onPageEnd(viewName)
        }

        /**
         * 事件埋点，不带页面编码
         */
        @JvmStatic
        fun trackEvent(context: Context, eventId: String, eventParams: Map<String, Any?>) {
            QtTrackAgent.onEventObject(context, eventId, eventParams)
            try {
//                SpmLogUtil.print(Gson().toJson(eventParams))
                val builder = StringBuilder("======").append("\n")
                builder.append("eventId = $eventId").append("\n")
                if (eventParams.containsKey("spm_cnt")) {
                    builder.append("spm_cnt = ").append(eventParams["spm_cnt"]).append("\n")
                }
                if (eventParams.containsKey("scm_cnt")) {
                    builder.append("scm_cnt = ").append(eventParams["scm_cnt"]).append("\n")
                }
                eventParams.forEach {
                    if (it.key != "spm_cnt" && it.key != "scm_cnt") {
                        builder.append("${it.key} = ${it.value}").append("\n")
                    }
                }
                builder.append("==========================================").append("\n")
                SpmLogUtil.print(builder.toString())
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }

        /**
         * 事件埋点，带页面编码
         */
        @JvmStatic
        fun trackEvent(context: Context, eventId: String, eventParams: Map<String, Any?>, pageName: String) {
            QtTrackAgent.onEventObject(context, eventId, eventParams, pageName)
        }

    }
}