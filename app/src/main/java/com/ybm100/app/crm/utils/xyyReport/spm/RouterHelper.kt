package com.ybmmarket20.xyyreport.spm

import android.content.Context
import android.net.Uri

object RouterHelper {

//    @JvmStatic
//    fun makeupUrlForSpm(context: Context, url: String?, spmCnt: String?, scmCnt: String?): String? {
//        if (context !is XyyReportActivity) return url
//        val spmUrl = context.getSpmUrl()
//        val scmUrl = context.getScmUrl()
//        val spmParams = "spmCnt=$spmCnt&scmCnt=$scmCnt&spmUrl=$spmUrl&scmUrl=$scmUrl"
//        val uri = Uri.parse(url)
//        val pathList = uri.pathSegments
//        val queryList = uri.queryParameterNames
//        val host = uri.host
//        val scheme = uri.scheme
//        return if (queryList.isEmpty()) {
//            "$url?$spmParams"
//        } else {
//            val pathPre = if (pathList.isEmpty()) "" else { "/" }
//            val c ="$scheme://$host$pathPre${pathList.joinToString("/")}"
//            val o = "$c?"
//            val n = "$c?$spmParams&"
//            return url?.replace(o, n)?: url
//        }
//    }
}