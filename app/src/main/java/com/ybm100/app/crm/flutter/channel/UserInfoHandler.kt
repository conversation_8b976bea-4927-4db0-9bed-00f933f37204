package com.ybm100.app.crm.flutter.channel

import android.util.Log
import androidx.fragment.app.FragmentActivity
import com.google.gson.Gson
import com.xyy.flutter.container.container.bridge.BaseHandler
import com.ybm100.app.crm.utils.SharedPrefManager
import com.ybmmarket20.xyyreport.session.SessionManager.Companion.get

//class UserInfoHandler : BaseHandler() {
//    override fun handle(activity: FragmentActivity, params: Map<String, Any?>) {
//        val userInfo = SharedPrefManager.getInstance().userInfo
//        if (userInfo != null) {
//            success(Gson().toJson(userInfo))
//        } else {
//            success("{}")
//        }
//    }
//}
class UserInfoHandler : BaseHandler() {
    override fun handle(activity: FragmentActivity, params: Map<String, Any?>) {
        val userInfo = SharedPrefManager.getInstance().userInfo

        if (userInfo != null) {
            //取出sessionId
            val sessionId = get().getSession()

            // 将userInfo转换为对象，然后添加sessionId
            val gson = Gson()
            val userInfoJson = gson.toJson(userInfo)
            val userInfoMap = gson.fromJson(userInfoJson, Map::class.java) as MutableMap<String, Any?>
            userInfoMap["sessionId"] = sessionId

            val a = gson.toJson(userInfoMap)

            Log.i(
                "QuickTrackingSDK",
                "正式初始化成功 - userInfoMap: $a"
            )
            success(gson.toJson(userInfoMap))
        } else {
            success("{}")
        }
    }
}